<script lang="ts">
    import {SearchX, X} from '@lucide/svelte';
    import EmptyState from '$lib/components/shared/states/EmptyState.svelte';

    interface Props {
        onSearchChange: (value: string) => void;
    }

    let {onSearchChange}: Props = $props();

    function handleClear() {
        onSearchChange('');
    }
</script>

<EmptyState
    icon={SearchX}
    title="Nothing found"
    description="No match for your search criteria."
    action={{
        label: 'Clear search',
        icon: X,
        onclick: handleClear
    }}
/>
