<script lang="ts">
    import {onMount} from 'svelte';
    import {toast} from 'svelte-sonner';
    import ClientsSearchBar from './ClientsSearchBar.svelte';
    import ClientsToolbar from './ClientsToolbar.svelte';
    import ClientsLoadingState from './ClientsLoadingState.svelte';
    import ClientsGrid from './ClientsGrid.svelte';
    import ClientsSearchEmptyState from './ClientsSearchEmptyState.svelte';
    import ClientsGeneralEmptyState from './ClientsGeneralEmptyState.svelte';
    import ErrorState from '$lib/components/shared/states/ErrorState.svelte';
    import {
        clientsStore,
        filtersStore,
        filteredClients,
        clientsActions,
    } from '$lib/stores/clientsStore';

    // Reactive state from stores
    $: ({isLoading, error} = $clientsStore);
    $: filters = $filtersStore;
    $: clients = $filteredClients;

    // Event handlers
    function handleSearchChange(value: string) {
        clientsActions.setSearchQuery(value);
    }

    function handleRefresh() {
        clientsActions.fetchClients(true);
    }

    function handleSortChange(sortBy: typeof filters.sortBy, sortOrder: typeof filters.sortOrder) {
        clientsActions.setSorting(sortBy, sortOrder);
    }

    function handleInviteClient() {
        // TODO: Implement invite client functionality
        toast.info('Invite client functionality coming soon!');
    }

    // Initialize data on mount
    onMount(() => {
        clientsActions.fetchClients();
    });
</script>

<div class="space-y-6">
    <!-- Search and Toolbar -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <ClientsSearchBar
                bind:value={filters.searchQuery}
                onValueChange={handleSearchChange}
                onClear={handleSearchClear}
                disabled={isLoading}
        />

        <ClientsToolbar
                {isLoading}
                clientsCount={clients.length}
                {filters}
                onRefresh={handleRefresh}
                onSortChange={handleSortChange}
                onAddClient={handleInviteClient}
        />
    </div>

    <!-- Client Cards Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6 relative">
        {#if isLoading}
            <ClientsLoadingState />
        {:else if error}
            <ClientsLoadingState />
            <div class="absolute inset-0 flex items-center justify-center z-20">
                <ErrorState
                    error={error}
                    onRetry={handleRefresh}
                />
            </div>
        {:else if clients.length === 0}
            <ClientsLoadingState />
            <div class="absolute inset-0 flex items-center justify-center z-20">
                {#if filters.searchQuery}
                    <ClientsSearchEmptyState onClear={handleSearchClear} />
                {:else}
                    <ClientsGeneralEmptyState onInvite={handleInviteClient} />
                {/if}
            </div>
        {:else}
            <ClientsGrid {clients} />
        {/if}
    </div>
</div>
