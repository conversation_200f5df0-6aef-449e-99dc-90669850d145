<script lang="ts">
    import {Search} from '@lucide/svelte';
    import {Input} from '$lib/components/ui/input';

    interface Props {
        value: string;
        placeholder?: string;
        disabled?: boolean;
        onValueChange?: (value: string) => void;
    }

    let {
        value = $bindable(),
        placeholder = 'Search...',
        disabled = false,
        onValueChange,
    }: Props = $props();

    function handleInput(event: Event) {
        const target = event.target as HTMLInputElement;
        value = target.value;
        onValueChange?.(target.value);
    }
</script>

<div class="relative w-full sm:w-80">
    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <Search class="h-4 w-4 text-muted-foreground"/>
    </div>

    <Input
            type="search"
            {placeholder}
            {disabled}
            class="pl-10 pr-10"
            bind:value
            oninput={handleInput}
    />

    {#if value}
        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
            <Button.Root
                    variant="ghost"
                    size="sm"
                    class="h-6 w-6 p-0 hover:bg-muted"
                    onclick={handleClear}
            >
                <X class="h-3 w-3"/>
            </Button.Root>
        </div>
    {/if}
</div>
